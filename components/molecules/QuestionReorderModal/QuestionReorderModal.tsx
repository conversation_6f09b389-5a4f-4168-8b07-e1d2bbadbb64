'use client';

import React, { useState, useEffect } from 'react';
import { GripVertical, ArrowUp, ArrowDown } from 'lucide-react';
import { Button } from '@/components/atoms/Button/Button';
import Icon from '@/components/atoms/Icon/Icon';
import { Question } from '@/components/molecules/QuestionListingView/QuestionListingView';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

interface QuestionReorderModalProps {
  isOpen: boolean;
  onClose: () => void;
  questions: Question[];
  questionIds?: string[];
  onSaveReorder: (reorderedQuestions: Question[], questionIds?: string[]) => Promise<void>;
  isLoading?: boolean;
}

interface SortableQuestionItemProps {
  question: Question;
  index: number;
  isDragging?: boolean;
}

const SortableQuestionItem: React.FC<SortableQuestionItemProps> = ({
  question,
  index,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: question.id || `question-${index}` });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  // Get question type display
  const getQuestionTypeDisplay = (type: string) => {
    const typeMap: Record<string, string> = {
      'multiple_choice': 'MC',
      'true_false': 'T/F',
      'fill_in_the_blank': 'Fill',
      'short_answer': 'Short',
      'essay': 'Essay',
      'matching': 'Match',
      'ordering': 'Order',
      'calculation': 'Calc',
      'diagram': 'Diagram',
      'long_answer': 'Long',
    };
    return typeMap[type] || type.toUpperCase();
  };

  // Truncate question content for preview and strip HTML
  const truncateContent = (content: string, maxLength: number = 80) => {
    // Strip HTML tags and decode HTML entities
    const strippedContent = content
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/&nbsp;/g, ' ') // Replace &nbsp; with space
      .replace(/&amp;/g, '&') // Replace &amp; with &
      .replace(/&lt;/g, '<') // Replace &lt; with <
      .replace(/&gt;/g, '>') // Replace &gt; with >
      .replace(/&quot;/g, '"') // Replace &quot; with "
      .replace(/&#39;/g, "'") // Replace &#39; with '
      .trim();

    if (strippedContent.length <= maxLength) return strippedContent;
    return strippedContent.substring(0, maxLength) + '...';
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        bg-white border border-gray-200 rounded-lg p-2 sm:p-4 mb-2 sm:mb-3 shadow-sm
        transition-all duration-200 hover:shadow-md touch-manipulation
        ${isDragging ? 'opacity-50 shadow-lg scale-105' : ''}
      `}
    >
      <div className="flex items-center gap-2 sm:gap-3">
        {/* Drag Handle */}
        <div
          {...attributes}
          {...listeners}
          className="flex-shrink-0 p-2 cursor-grab active:cursor-grabbing hover:bg-gray-100 rounded-md transition-colors min-w-[44px] min-h-[44px] sm:min-w-auto sm:min-h-auto flex items-center justify-center"
          aria-label={`Drag to reorder question ${index + 1}`}
        >
          <GripVertical size={20} className="text-gray-400 sm:w-5 sm:h-5" />
        </div>

        {/* Question Number */}
        <div className="flex-shrink-0 w-8 h-8 sm:w-8 sm:h-8 bg-blue-100 text-blue-700 rounded-full flex items-center justify-center text-sm font-semibold">
          {index + 1}
        </div>

        {/* Question Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1 sm:mb-1 flex-wrap">
            {/* Question Type Badge */}
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
              {getQuestionTypeDisplay(question.type)}
            </span>

            {/* Subject Badge (if available) */}
            {question.subject && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700">
                {question.subject}
              </span>
            )}
          </div>

          {/* Question Content Preview */}
          <p className="text-sm sm:text-sm text-gray-600 leading-relaxed">
            {truncateContent(question.content, typeof window !== 'undefined' && window.innerWidth < 640 ? 50 : 80)}
          </p>
        </div>
      </div>
    </div>
  );
};

export const QuestionReorderModal: React.FC<QuestionReorderModalProps> = ({
  isOpen,
  onClose,
  questions,
  questionIds = [],
  onSaveReorder,
  isLoading = false,
}) => {
  const [localQuestions, setLocalQuestions] = useState<Question[]>(questions);
  const [localQuestionIds, setLocalQuestionIds] = useState<string[]>(questionIds);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [isMobile, setIsMobile] = useState(false);

  // Screen size detection for responsive height calculation
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 640); // sm breakpoint
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // Initialize local state when modal opens or questions change
  useEffect(() => {
    if (isOpen) {
      setLocalQuestions(questions);
      setLocalQuestionIds(questionIds);
      setHasUnsavedChanges(false);
      setSaveError(null);
    }
  }, [isOpen, questions, questionIds]);

  // Set up drag and drop sensors with mobile optimization
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 10, // Slightly more distance for mobile to prevent accidental drags
        tolerance: 5,
        delay: 100, // Small delay to distinguish from scrolling
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Handle drag end
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      // Create a map of IDs to indices for faster lookup
      const idToIndex = new Map();
      localQuestions.forEach((q, index) => {
        const id = q.id || `question-${index}`;
        idToIndex.set(id, index);
      });

      const oldIndex = idToIndex.get(active.id);
      const newIndex = idToIndex.get(over.id);

      if (oldIndex !== undefined && newIndex !== undefined && oldIndex !== newIndex) {
        const newQuestions = arrayMove(localQuestions, oldIndex, newIndex);
        const newQuestionIds = localQuestionIds.length > 0
          ? arrayMove(localQuestionIds, oldIndex, newIndex)
          : [];

        setLocalQuestions(newQuestions);
        setLocalQuestionIds(newQuestionIds);
        setHasUnsavedChanges(true);
        setSaveError(null);
      }
    }
  };

  // Handle save
  const handleSave = async () => {
    if (!hasUnsavedChanges) return;

    setIsSaving(true);
    setSaveError(null);

    try {
      await onSaveReorder(
        localQuestions,
        localQuestionIds.length > 0 ? localQuestionIds : undefined
      );
      setHasUnsavedChanges(false);
      onClose();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to save question order';
      setSaveError(errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    setLocalQuestions(questions);
    setLocalQuestionIds(questionIds);
    setHasUnsavedChanges(false);
    setSaveError(null);
    onClose();
  };

  // Handle keyboard events and accessibility
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        handleCancel();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden';

      // Focus management - focus the modal when it opens
      const modal = document.querySelector('[role="dialog"]') as HTMLElement;
      if (modal) {
        modal.focus();
      }
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black/60 backdrop-blur-sm flex sm:items-center sm:justify-center lg:left-[310px] lg:w-[calc(100vw-310px)] z-50 sm:p-4"
      style={{ zIndex: 9999 }}
      role="dialog"
      aria-modal="true"
      aria-labelledby="reorder-modal-title"
      aria-describedby="reorder-modal-description"
    >
      <div
        className="bg-white w-full h-full sm:h-auto sm:max-w-lg sm:max-h-[90vh] rounded-none sm:rounded-2xl shadow-2xl overflow-hidden flex flex-col"
        tabIndex={-1}
      >
            {/* Header */}
            <div className="bg-blue-600 text-white p-2 sm:p-4 flex justify-between items-center min-h-[56px] sm:min-h-auto">
              <div className="flex items-center gap-2 sm:gap-3">
                <ArrowUp size={18} className="flex-shrink-0 sm:w-5 sm:h-5" aria-hidden="true" />
                <h2 id="reorder-modal-title" className="text-base sm:text-lg font-semibold truncate">
                  Reorder Questions
                </h2>
              </div>
              <Button
                onClick={handleCancel}
                variant="ghost"
                className="rounded-full p-2 text-white bg-white/20 hover:bg-red-500 hover:bg-opacity-90 !min-w-[44px] !min-h-[44px] transition-colors duration-200 flex-shrink-0"
                aria-label="Close"
                disabled={isSaving}
                iconProps={{
                  variant: "x",
                  size: 4,
                  className: "text-white"
                }}
              >
                Close
                </Button>
            </div>
            {/* Question List */}
            <div
              className="flex-1 overflow-y-auto p-2 sm:p-4"
              style={{
                maxHeight: isMobile ? 'calc(100vh - 120px)' : 'calc(100vh - 160px)',
                minHeight: isMobile ? '250px' : '300px'
              }}
            >
              <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
              >
                <SortableContext
                  items={localQuestions.map((q, index) => q.id || `question-${index}`)}
                  strategy={verticalListSortingStrategy}
                >
                  {localQuestions.map((question, index) => (
                    <SortableQuestionItem
                      key={question.id || `question-${index}`}
                      question={question}
                      index={index}
                    />
                  ))}
                </SortableContext>
              </DndContext>
            </div>

            {/* Footer */}
            <div className="p-2 sm:p-4 border-t border-gray-200 bg-white flex-shrink-0">

              {/* Error Message */}
              {saveError && (
                <div className="mb-2 sm:mb-3 p-2 sm:p-3 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Icon variant="alert-triangle" size={4} className="text-red-600 flex-shrink-0" />
                    <p className="text-sm text-red-700">{saveError}</p>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-0">
                <div className="text-xs sm:text-sm text-gray-600 text-center sm:text-left">
                  {localQuestions.length} question{localQuestions.length !== 1 ? 's' : ''}
                  {hasUnsavedChanges && (
                    <span className="block sm:inline sm:ml-2 text-orange-600 font-medium">
                      <span className="hidden sm:inline">• </span>Unsaved changes
                    </span>
                  )}
                </div>

                <div className="flex items-center gap-2 sm:gap-2">
                  <Button
                    variant="outline"
                    onClick={handleCancel}
                    disabled={isSaving}
                    className="flex-1 sm:flex-none px-3 sm:px-4 py-2 sm:py-2 text-sm min-h-[44px] sm:min-h-auto"
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="primary"
                    onClick={handleSave}
                    disabled={!hasUnsavedChanges || isSaving}
                    className={`flex-1 sm:flex-none px-3 sm:px-4 py-2 sm:py-2 text-sm min-w-[100px] sm:min-w-[100px] min-h-[44px] sm:min-h-auto ${
                      hasUnsavedChanges ? 'ring-2 ring-orange-300' : ''
                    }`}
                    style={{
                      opacity: hasUnsavedChanges ? 1 : 0.6,
                      border: hasUnsavedChanges ? '2px solid orange' : undefined
                    }}
                    iconProps={{
                      variant: "check",
                      size: 4,
                      className: "text-white"
                    }}
                  >
                    {isSaving ? (
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin" />
                        Saving...
                      </div>
                    ) : (
                      <>
                        Save Order
                        {hasUnsavedChanges && <span className="ml-1 text-xs">●</span>}
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
      </div>
    </div>
  );
};
